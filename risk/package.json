{"name": "traffic-risk-detector", "version": "1.0.0", "description": "基于业界最佳实践的流量监控风险检测算法", "main": "risk-detecter.js", "scripts": {"test": "node test-risk-detector.js", "test:verbose": "node test-risk-detector.js --verbose", "test:watch": "nodemon test-risk-detector.js", "demo": "node -e \"const {detectRiskFromWindows} = require('./test-risk-detector.js'); const result = detectRiskFromWindows([2000,2100,1900,2200], [1000,1000,1000,1000]); console.log('Demo Result:', {risk: (result.risk*100).toFixed(1)+'%', type: result.dataRelation.relationType});\""}, "keywords": ["traffic-monitoring", "risk-detection", "anomaly-detection", "flow-analysis", "best-practices"], "author": "AI Assistant", "license": "MIT", "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=14.0.0"}}