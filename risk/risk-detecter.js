/**
 * detectRiskFromWindows (自适应优化版本)
 *
 * 输入参数:
 *  - currentWindow: 数字数组 或 包含 {time, value} 对象的数组 (最新->最旧 或 最旧->最新 都可以)
 *  - yoyWindow:     同长度的数组，格式相同 (同比窗口)
 *  - config:        可选配置参数 (见默认值)
 *
 * 输出结果: {
 *   risk: float 0..1,            // 风险概率，可直接用作百分比展示
 *   confidence: float 0..1,       // 置信度
 *   riskBand: {lower, upper},     // 风险置信区间
 *   raw: number,                  // 原始评分
 *   S: severity (0..5),           // 严重性
 *   M: magnitude (0..2),          // 幅度
 *   P: persistence (0..3),        // 原始持续性评分
 *   EP: extendedPersistence (0..5), // 扩展持续性评分
 *   TD: trendDeviation (boolean),   // 趋势偏离检测
 *   consCount: 窗口末尾连续异常数量,
 *   trendBias: 趋势偏离详情,
 *   perPoint: [{time, C, Y, B, r, z, ratio, isAnomaly}, ...],
 *   a, b,
 *   details: {medianSigma, cusumTriggered, trendAnalysis, ...},
 *   adaptiveInfo: 自适应调节信息
 * }
 */

function detectRiskFromWindows(currentWindow, yoyWindow, config = {}) {
    // ---------- 默认配置 ----------
    const defaultConfig = {
        ewmaSpan: config.ewmaSpan ?? 3,
        madWindow: config.madWindow ?? 7,
        beta: config.beta ?? 0.02,
        z_th: config.z_th ?? 2.5,
        ratio_th: config.ratio_th ?? 0.12,
        tau: config.tau ?? 3,
        gamma: config.gamma ?? 0.5,
        L: config.L ?? 3, // 原始连续分钟数阈值
        enableCUSUM: config.enableCUSUM ?? true,
        eps: 1e-9,

        // 持续性和趋势配置
        targetConsecutiveMinutes: config.targetConsecutiveMinutes ?? 5, // 目标连续异常分钟数
        trendDeviationWindow: config.trendDeviationWindow ?? 10, // 趋势偏离检测窗口
        trendBiasThreshold: config.trendBiasThreshold ?? 0.8, // 80%同向偏离阈值
        extendedPersistenceWeight: config.extendedPersistenceWeight ?? 1.5, // 扩展持续性权重

        // 自适应调节配置
        enableAdaptiveTuning: config.enableAdaptiveTuning ?? true, // 启用自适应调节
        adaptiveStrength: config.adaptiveStrength ?? 1.0, // 调节强度 0-1
    };

    // ---------- 工具函数 ----------
    const toSeries = (arr) => {
        if (!Array.isArray(arr))
            return {
                times: [],
                vals: [],
            };
        if (arr.length === 0)
            return {
                times: [],
                vals: [],
            };
        const times = [];
        const vals = [];
        for (let i = 0; i < arr.length; i++) {
            const it = arr[i];
            if (it == null) continue;
            if (typeof it === 'number') {
                times.push(i);
                vals.push(it);
            } else if (typeof it === 'object') {
                // 接受 {time, value} 或 {t, v} 格式
                const v = it.value ?? it.val ?? it.v ?? it.count ?? it.y ?? it;
                const t = it.time ?? it.t ?? i;
                const n = Number(v);
                if (!Number.isFinite(n)) {
                    times.push(t);
                    vals.push(0);
                } else {
                    times.push(t);
                    vals.push(n);
                }
            } else if (typeof it === 'string') {
                const n = Number(it);
                times.push(i);
                vals.push(Number.isFinite(n) ? n : 0);
            }
        }
        return {times, vals};
    };

    const median = (arr) => {
        if (!arr || arr.length === 0) return 0;
        const a = Array.from(arr).sort((x, y) => x - y);
        const m = Math.floor(a.length / 2);
        return a.length % 2 === 1 ? a[m] : (a[m - 1] + a[m]) / 2;
    };

    const mad = (arr) => {
        if (!arr || arr.length === 0) return 0;
        const med = median(arr);
        const devs = arr.map((x) => Math.abs(x - med));
        return median(devs);
    };

    const mean = (arr) => {
        if (!arr || arr.length === 0) return 0;
        return arr.reduce((sum, x) => sum + x, 0) / arr.length;
    };

    const stddev = (arr) => {
        if (!arr || arr.length <= 1) return 0;
        const m = mean(arr);
        const variance = arr.reduce((sum, x) => sum + (x - m) ** 2, 0) / (arr.length - 1);
        return Math.sqrt(variance);
    };

    const ewma = (vals, span) => {
        if (!vals || vals.length === 0) return [];
        const s = [];
        const alpha = 2 / (Math.max(1, span) + 1);
        let agg = vals[0] ?? 0;
        s[0] = agg;
        for (let i = 1; i < vals.length; i++) {
            agg = alpha * vals[i] + (1 - alpha) * agg;
            s[i] = agg;
        }
        return s;
    };

    const rollingSlice = (arr, idx, w) => {
        const start = Math.max(0, idx - w + 1);
        return arr.slice(start, idx + 1);
    };

    // ---------- 同比数据特征分析 ----------
    const analyzeYoyCharacteristics = (yoyVals) => {
        const positiveVals = yoyVals.filter((v) => v > 0);
        if (positiveVals.length === 0) {
            return {
                median: 0,
                mean: 0,
                cv: 1,
                relativeMAD: 1,
                logScale: 0,
                burstiness: 1,
                zeroRatio: 1,
                isEmpty: true,
            };
        }

        const med = median(positiveVals);
        const m = mean(positiveVals);
        const std = stddev(positiveVals);
        const madVal = mad(positiveVals);

        return {
            median: med,
            mean: m,
            cv: std / Math.max(m, 1), // 变异系数
            relativeMAD: madVal / Math.max(med, 1), // 相对离散度
            logScale: Math.log10(Math.max(med, 1) + 1), // 对数规模
            burstiness: Math.max(...positiveVals) / Math.max(med, 1), // 爆发性
            zeroRatio: (yoyVals.length - positiveVals.length) / yoyVals.length, // 零值比例
            isEmpty: false,
        };
    };

    // ---------- 计算自适应调节系数 ----------
    const computeAdjustmentFactors = (yoyStats) => {
        const {median, cv, relativeMAD, logScale, burstiness} = yoyStats;

        // 规模调节因子（连续函数）
        // 小流量(median<100) → 大调节(~2.0), 大流量(median>10000) → 小调节(~0.8)
        const scaleAdjustment = 2.0 / (1 + Math.exp(0.5 * (logScale - 2)));

        // 变异性调节因子
        // cv越大，调节系数越大
        const variabilityAdjustment = 1 + Math.tanh(2 * cv) * 0.5; // 范围: [1.0, 1.5]

        // 稳定性调节因子
        // 基于相对MAD的连续调节
        const stabilityAdjustment = 1 + Math.min(relativeMAD, 1) * 0.3; // 范围: [1.0, 1.3]

        // 爆发性调节因子
        // 处理突发流量特征
        const burstAdjustment = 1 + Math.log10(Math.max(burstiness, 1)) * 0.2;

        return {
            scale: scaleAdjustment,
            variability: variabilityAdjustment,
            stability: stabilityAdjustment,
            burst: burstAdjustment,

            // 组合调节系数
            combined: {
                // 阈值类参数的调节
                thresholdMultiplier:
                    scaleAdjustment *
                    Math.sqrt(variabilityAdjustment) *
                    Math.sqrt(stabilityAdjustment),

                // 窗口类参数的调节
                windowMultiplier:
                    Math.sqrt(scaleAdjustment) * (1 + Math.log10(median + 1) / 10),

                // 敏感度类参数的调节
                sensitivityMultiplier: scaleAdjustment * variabilityAdjustment,

                // 最小有意义变化量的动态计算
                minMeaningfulChange:
                    Math.sqrt(median) * (1 + cv) * Math.max(1, Math.log10(median + 1)),
            },
        };
    };

    // ---------- 应用自适应参数调节 ----------
    const adaptiveParameterTuning = (baseConfig, yoyStats, strength = 1.0) => {
        if (yoyStats.isEmpty) {
            return {...baseConfig, minMeaningfulChange: 0, adjustmentInfo: null};
        }

        const adjustments = computeAdjustmentFactors(yoyStats);

        // 根据调节强度混合原始参数和调节后参数
        const mix = (original, adjusted) => {
            return original + (adjusted - original) * strength;
        };

        const adaptedConfig = {
            ...baseConfig,

            // 阈值参数调节
            z_th: mix(
                baseConfig.z_th,
                baseConfig.z_th * adjustments.combined.thresholdMultiplier
            ),
            ratio_th: mix(
                baseConfig.ratio_th,
                baseConfig.ratio_th * adjustments.combined.thresholdMultiplier
            ),

            // 平滑参数调节
            ewmaSpan: Math.round(
                mix(baseConfig.ewmaSpan, baseConfig.ewmaSpan * adjustments.combined.windowMultiplier)
            ),
            madWindow: Math.min(
                15,
                Math.round(
                    mix(
                        baseConfig.madWindow,
                        baseConfig.madWindow * adjustments.combined.windowMultiplier
                    )
                )
            ),

            // 噪声容忍参数
            beta: mix(
                baseConfig.beta,
                baseConfig.beta * adjustments.combined.sensitivityMultiplier
            ),

            // 最小有意义变化
            minMeaningfulChange: adjustments.combined.minMeaningfulChange,

            // 保存调节信息
            adjustmentInfo: {
                yoyStats,
                adjustments,
                applied: true,
            },
        };

        // 参数合理性校验
        adaptedConfig.z_th = Math.max(1.5, Math.min(5, adaptedConfig.z_th));
        adaptedConfig.ratio_th = Math.max(0.05, Math.min(0.5, adaptedConfig.ratio_th));
        adaptedConfig.ewmaSpan = Math.max(2, Math.min(10, adaptedConfig.ewmaSpan));
        adaptedConfig.madWindow = Math.max(5, Math.min(15, adaptedConfig.madWindow));

        return adaptedConfig;
    };

    // ---------- 扩展持续性检测函数 ----------
    const getExtendedPersistence = (perPoint, targetMinutes) => {
        let consecutiveCount = 0;
        for (let i = perPoint.length - 1; i >= 0; i--) {
            if (perPoint[i].isAnomaly) {
                consecutiveCount += 1;
            } else {
                break;
            }
        }

        // 细粒度的持续性评分
        if (consecutiveCount >= targetMinutes) return 5;
        if (consecutiveCount >= Math.ceil(targetMinutes * 0.8)) return 4;
        if (consecutiveCount >= Math.ceil(targetMinutes * 0.6)) return 3;
        if (consecutiveCount >= Math.ceil(targetMinutes * 0.4)) return 2;
        if (consecutiveCount >= 1) return 1;
        return 0;
    };

    // ---------- 趋势偏离检测函数 ----------
    const detectTrendDeviation = (perPoint, windowSize, biasThreshold) => {
        if (perPoint.length < windowSize) {
            windowSize = perPoint.length;
        }

        const recentPoints = perPoint.slice(-windowSize);
        const recentResiduals = recentPoints.map((p) => p.r);

        // 计算正向和负向偏离的比例
        const positiveCount = recentResiduals.filter((r) => r > 0).length;
        const negativeCount = recentResiduals.filter((r) => r < 0).length;
        const zeroCount = recentResiduals.filter((r) => r === 0).length;

        const totalCount = recentResiduals.length;
        const positiveBias = positiveCount / totalCount;
        const negativeBias = negativeCount / totalCount;
        const maxBias = Math.max(positiveBias, negativeBias);

        // 计算偏离强度
        const avgAbsResidual =
            recentResiduals.reduce((sum, r) => sum + Math.abs(r), 0) / totalCount;
        const avgPositiveResidual =
            recentResiduals.filter((r) => r > 0).reduce((sum, r) => sum + r, 0) /
            (positiveCount || 1);
        const avgNegativeResidual =
            recentResiduals.filter((r) => r < 0).reduce((sum, r) => sum + Math.abs(r), 0) /
            (negativeCount || 1);

        const isDeviation = maxBias >= biasThreshold;
        const direction =
            positiveBias > negativeBias
                ? 'UP'
                : negativeBias > positiveBias
                    ? 'DOWN'
                    : 'NEUTRAL';

        return {
            isDeviation,
            direction,
            bias: maxBias,
            positiveBias,
            negativeBias,
            avgAbsResidual,
            avgPositiveResidual,
            avgNegativeResidual,
            windowSize,
            positiveCount,
            negativeCount,
            zeroCount,
        };
    };

    // ---------- 数据关系分析和预处理 ----------
    const analyzeDataRelationship = (currentVals, yoyVals) => {
        const cStats = {
            median: median(currentVals),
            mean: mean(currentVals),
            min: Math.min(...currentVals),
            max: Math.max(...currentVals),
            range: Math.max(...currentVals) - Math.min(...currentVals)
        };

        const yStats = {
            median: median(yoyVals),
            mean: mean(yoyVals),
            min: Math.min(...yoyVals),
            max: Math.max(...yoyVals),
            range: Math.max(...yoyVals) - Math.min(...yoyVals)
        };

        // 计算量级差异
        const magnitudeRatio = Math.log10(Math.max(cStats.median, 1)) - Math.log10(Math.max(yStats.median, 1));
        const scaleRatio = cStats.median / Math.max(yStats.median, 1);

        // 判断数据关系类型 - 针对请求量监控优化
        let relationType = 'UNKNOWN';
        let processingStrategy = 'DIRECT';
        let riskLevel = 'NORMAL';

        // 首先检查是否是标准格式的YoY数据
        const isRatioFormat = yStats.median >= 0.5 && yStats.median <= 2.0 && yStats.range <= 1.0;
        const isPercentageFormat = yStats.min >= 80 && yStats.max <= 120 && yStats.range <= 50;

        // 优先检查标准格式，避免误判
        if (isRatioFormat && !isPercentageFormat) {
            // YoY是倍数格式(0.5-2.0)，正常处理
            relationType = 'RATIO_MULTIPLIER';
            processingStrategy = 'RATIO_BASED';
            riskLevel = 'NORMAL';
        } else if (isPercentageFormat) {
            // YoY是百分比格式(80-120)，但需要检查是否真的是正常范围
            if (magnitudeRatio >= 3) {
                // 即使是百分比格式，但量级差异巨大，仍然是异常
                relationType = 'EXTREME_SURGE';
                processingStrategy = 'EXTREME_RISK_MODE';
                riskLevel = 'CRITICAL';
            } else {
                relationType = 'PERCENTAGE';
                processingStrategy = 'PERCENTAGE_BASED';
                riskLevel = 'NORMAL';
            }
        } else if (magnitudeRatio >= 3) {
            // 当前请求量比同比高1000倍以上 - 极高风险！
            relationType = 'EXTREME_SURGE';
            processingStrategy = 'EXTREME_RISK_MODE';
            riskLevel = 'CRITICAL';
        } else if (magnitudeRatio >= 1.5) {
            // 当前请求量比同比高30倍以上 - 高风险
            relationType = 'MAJOR_SURGE';
            processingStrategy = 'HIGH_RISK_MODE';
            riskLevel = 'HIGH';
        } else if (magnitudeRatio <= -1.5) {
            // 当前请求量比同比低30倍以上 - 可能是服务异常
            relationType = 'MAJOR_DROP';
            processingStrategy = 'HIGH_RISK_MODE';
            riskLevel = 'HIGH';
        } else if (Math.abs(magnitudeRatio) <= 0.5) {
            // 量级相近，正常的同比数据
            relationType = 'SAME_SCALE';
            processingStrategy = 'DIRECT';
            riskLevel = 'NORMAL';
        } else if (scaleRatio >= 12) {
            // 基于绝对倍数的判断：12倍以上增长 (进一步降低阈值)
            relationType = 'SIGNIFICANT_SURGE';
            processingStrategy = 'MEDIUM_RISK_MODE';
            riskLevel = 'MEDIUM';
        } else if (scaleRatio <= 0.1) {
            // 基于绝对倍数的判断：下降到1/10以下
            relationType = 'SIGNIFICANT_DROP';
            processingStrategy = 'MEDIUM_RISK_MODE';
            riskLevel = 'MEDIUM';
        } else {
            // 其他情况，使用相对变化
            relationType = 'MIXED_SCALE';
            processingStrategy = 'ADAPTIVE_SCALING';
            riskLevel = 'LOW';
        }

        return {
            currentStats: cStats,
            yoyStats: yStats,
            magnitudeRatio,
            scaleRatio,
            relationType,
            processingStrategy,
            riskLevel,
            confidence: riskLevel === 'CRITICAL' ? 0.95 : // 极端情况高置信度
                       riskLevel === 'HIGH' ? 0.85 :
                       riskLevel === 'MEDIUM' ? 0.7 :
                       Math.max(0.1, 1 - Math.abs(magnitudeRatio) / 5)
        };
    };

    // ---------- 输入标准化 ----------
    const cur = toSeries(currentWindow);
    const yo = toSeries(yoyWindow);
    const N = Math.min(cur.vals.length, yo.vals.length);
    if (N <= 0) {
        return {
            risk: 0.01,
            confidence: 0,
            raw: 0,
            reason: 'empty_input',
        };
    }

    const Craw = cur.vals.slice(0, N);
    const Yraw = yo.vals.slice(0, N);

    // 分析数据关系
    const dataRelation = analyzeDataRelationship(Craw, Yraw);
    const times = cur.times.slice(0, N);

    // ---------- 多策略数据预处理 ----------
    const preprocessData = (currentVals, yoyVals, strategy, relation) => {
        let processedCurrent = [...currentVals];
        let processedYoy = [...yoyVals];
        let baselineMethod = 'LINEAR_REGRESSION';
        let scalingInfo = null;

        switch (strategy) {
            case 'DIRECT':
                // 直接使用原始数据
                break;

            case 'EXTREME_RISK_MODE':
                // 极端风险模式：请求量暴增1000倍以上
                // 直接使用原始数据，但会在风险评分中大幅加分
                scalingInfo = {
                    method: 'extreme_risk_detection',
                    surgeFactor: relation.scaleRatio,
                    magnitudeRatio: relation.magnitudeRatio
                };
                break;

            case 'HIGH_RISK_MODE':
                // 高风险模式：请求量异常30倍以上
                scalingInfo = {
                    method: 'high_risk_detection',
                    surgeFactor: relation.scaleRatio,
                    magnitudeRatio: relation.magnitudeRatio
                };
                break;

            case 'MEDIUM_RISK_MODE':
                // 中等风险模式：请求量异常10倍以上
                scalingInfo = {
                    method: 'medium_risk_detection',
                    surgeFactor: relation.scaleRatio,
                    magnitudeRatio: relation.magnitudeRatio
                };
                break;

            case 'RATIO_BASED':
                // YoY作为倍数，转换为期望值
                processedYoy = yoyVals.map(y => y * relation.currentStats.median);
                scalingInfo = { method: 'ratio_scaling', factor: relation.currentStats.median };
                break;

            case 'PERCENTAGE_BASED':
                // YoY作为百分比，转换为期望值
                processedYoy = yoyVals.map(y => (y / 100) * relation.currentStats.median);
                scalingInfo = { method: 'percentage_scaling', baseline: relation.currentStats.median };
                break;

            case 'RELATIVE_CHANGE':
                // 使用相对变化率进行比较
                const cBaseline = currentVals[0] || relation.currentStats.median;
                const yBaseline = yoyVals[0] || relation.yoyStats.median;

                processedCurrent = currentVals.map(c => (c / cBaseline - 1) * 100); // 转为百分比变化
                processedYoy = yoyVals.map(y => (y / yBaseline - 1) * 100);

                baselineMethod = 'RELATIVE_CHANGE';
                scalingInfo = {
                    method: 'relative_change',
                    currentBaseline: cBaseline,
                    yoyBaseline: yBaseline
                };
                break;

            case 'ADAPTIVE_SCALING':
                // 自适应缩放：将YoY缩放到Current的量级
                const scaleFactor = relation.currentStats.median / Math.max(relation.yoyStats.median, 1);
                processedYoy = yoyVals.map(y => y * scaleFactor);
                scalingInfo = { method: 'adaptive_scaling', factor: scaleFactor };
                break;
        }

        return {
            current: processedCurrent,
            yoy: processedYoy,
            baselineMethod,
            scalingInfo
        };
    };

    const preprocessed = preprocessData(Craw, Yraw, dataRelation.processingStrategy, dataRelation);
    const C_processed = preprocessed.current;
    const Y_processed = preprocessed.yoy;

    // ---------- 同比数据分析和自适应参数调节 ----------
    let cfg = {...defaultConfig};
    let yoyStats = null;
    let adaptiveInfo = null;

    if (cfg.enableAdaptiveTuning) {
        yoyStats = analyzeYoyCharacteristics(Y_processed);
        cfg = adaptiveParameterTuning(cfg, yoyStats, cfg.adaptiveStrength);
        adaptiveInfo = cfg.adjustmentInfo;
    }

    // ---------- 平滑处理 ----------
    const C = ewma(C_processed, cfg.ewmaSpan);
    const Y = ewma(Y_processed, cfg.ewmaSpan);

    // ---------- 鲁棒仿射校准 a, b ----------
    const eps = cfg.eps;
    const ratios = [];
    const diffs = [];
    for (let i = 0; i < N; i++) {
        const denom = (Y[i] ?? 0) + eps;
        ratios.push((C[i] ?? 0) / denom);
    }
    const a = median(ratios);
    for (let i = 0; i < N; i++) {
        diffs.push((C[i] ?? 0) - a * (Y[i] ?? 0));
    }
    const b = median(diffs);

    // ---------- 基线和残差 ----------
    const B = new Array(N);
    const R = new Array(N);
    for (let i = 0; i < N; i++) {
        B[i] = a * (Y[i] ?? 0) + b;
        R[i] = (C[i] ?? 0) - B[i];
    }

    // ---------- 计算每个点的sigma、z、ratio、isAnomaly ----------
    const perPoint = [];
    const w = Math.max(1, cfg.madWindow);
    const sigmas = new Array(N).fill(0);
    for (let i = 0; i < N; i++) {
        const Ywin = rollingSlice(Y, i, w);
        const madY = mad(Ywin);
        const sigmaY = 1.4826 * madY;
        const Rwin = rollingSlice(R, i, w);
        const madR = mad(Rwin);
        const sigmaR = 1.4826 * madR;
        const sigma_i =
            Math.sqrt((a * sigmaY) ** 2 + sigmaR ** 2) + cfg.beta * Math.max(B[i], 0);
        sigmas[i] = sigma_i;

        // 斜率放松因子 kappa
        let db = 0;
        if (i >= 1) db = B[i] - B[i - 1];
        // 收集最近的绝对斜率
        const absSlopes = [];
        for (let j = Math.max(1, i - w + 1); j <= i; j++) {
            absSlopes.push(Math.abs(B[j] - B[j - 1] || 0));
        }
        const medianAbsSlope = Math.max(median(absSlopes), eps);
        const kappa = 1 + cfg.gamma * (Math.abs(db) / medianAbsSlope);
        const T = cfg.tau * kappa * (sigma_i + eps);

        const r = R[i];
        const z = r / (sigma_i + eps);
        const ratio = Math.abs(r) / Math.max(B[i], 1);
        const absoluteChange = Math.abs(r);

        // 增强的异常判定逻辑
        let isAnom = false;
        if (cfg.enableAdaptiveTuning && cfg.minMeaningfulChange > 0) {
            // 必须超过最小有意义变化
            if (absoluteChange >= cfg.minMeaningfulChange) {
                // 考虑历史变异性的动态判定
                const variabilityAdjustedZ = yoyStats ? z / (1 + yoyStats.cv * 0.5) : z;
                const variabilityAdjustedRatio = yoyStats
                    ? ratio / (1 + yoyStats.relativeMAD * 0.5)
                    : ratio;

                isAnom =
                    Math.abs(variabilityAdjustedZ) >= cfg.z_th ||
                    variabilityAdjustedRatio >= cfg.ratio_th;
            }
        } else {
            // 原始判定逻辑
            isAnom = Math.abs(z) >= cfg.z_th || ratio >= cfg.ratio_th;
        }

        perPoint.push({
            time: times[i],
            index: i,
            C: C[i],
            Y: Y[i],
            B: B[i],
            r,
            sigma: sigma_i,
            threshold: T,
            z,
            ratio,
            absoluteChange,
            isAnomaly: Boolean(isAnom),
            madY: madY,
            madR: madR,
            kappa,
        });
    }

    // ---------- 原始窗口末尾连续异常 ----------
    let consCount = 0;
    for (let i = N - 1; i >= 0; i--) {
        if (perPoint[i].isAnomaly) consCount += 1;
        else break;
    }
    // 限制持续性评分
    const P = Math.min(3, consCount);

    // ---------- 扩展持续性检测 ----------
    const EP = getExtendedPersistence(perPoint, cfg.targetConsecutiveMinutes);

    // ---------- 趋势偏离检测 ----------
    const trendAnalysis = detectTrendDeviation(
        perPoint,
        cfg.trendDeviationWindow,
        cfg.trendBiasThreshold
    );
    const TD = trendAnalysis.isDeviation;

    // ---------- 计算最后一个点的严重性和幅度 ----------
    const last = perPoint[N - 1];
    const z_clip = Math.min(Math.max(Math.abs(last.z), 0), 6);
    let S = 0;
    if (z_clip > 1) S = 5 * ((z_clip - 1) / 5); // 将 z 在 (1..6) 线性映射到 S (0..5)
    let M = 0;
    if (last.ratio >= 0.5) M = 2;
    else if (last.ratio >= 0.2) M = 1;

    // ---------- 可选的 CUSUM 检测残差缓慢漂移 ----------
    let cusumTriggered = false;
    let cusumBonus = 0;
    if (cfg.enableCUSUM) {
        // 使用中位数sigma设置 v 和 h
        const medianSigma = median(sigmas) || 1;
        const v = 0.25 * medianSigma;
        const h = 5 * medianSigma;
        let Spos = 0,
            Sneg = 0;
        for (let i = 0; i < N; i++) {
            const r = R[i];
            Spos = Math.max(0, Spos + r - v);
            Sneg = Math.max(0, Sneg - r - v);
            if (Spos > h || Sneg > h) {
                cusumTriggered = true;
                break;
            }
        }
        if (cusumTriggered) cusumBonus = 1;
    }

    // ---------- 请求量异常风险评估 ----------
    const requestVolumeRisk = (relation) => {
        let riskBonus = 0;
        let riskPenalty = 0;

        // 基于请求量变化的风险评估
        if (relation.relationType === 'EXTREME_SURGE') {
            // 极端请求量暴增：直接拉满风险
            riskBonus += 15; // 确保风险拉满到95%+
        } else if (relation.relationType === 'MAJOR_SURGE') {
            // 重大请求量暴增(30倍以上)
            riskBonus += 10; // 提高风险评分
        } else if (relation.relationType === 'MAJOR_DROP') {
            // 重大请求量下降（可能是服务异常）
            riskBonus += 8; // 提高风险评分
        } else if (relation.relationType === 'SIGNIFICANT_SURGE') {
            // 显著请求量暴增(12倍以上)
            riskBonus += 9; // 进一步提高风险评分
        } else if (relation.relationType === 'SIGNIFICANT_DROP') {
            // 显著请求量下降
            riskBonus += 7; // 提高风险评分
        } else if (relation.relationType === 'MIXED_SCALE') {
            riskBonus += 2;
        }

        // 极端情况不应该有惩罚
        if (relation.riskLevel === 'CRITICAL' || relation.riskLevel === 'HIGH') {
            riskPenalty = 0;
        } else if (relation.confidence < 0.3) {
            riskPenalty += 1;
        }

        return {
            bonus: riskBonus,
            penalty: riskPenalty,
            surgeFactor: relation.scaleRatio,
            riskLevel: relation.riskLevel
        };
    };

    const volumeRisk = requestVolumeRisk(dataRelation);

    // ---------- 增强的最终评分计算 ----------
    // 基础分数
    let raw = S + M + P + cusumBonus;

    // 扩展持续性加分
    const extendedPersistenceBonus = EP > P ? (EP - P) * cfg.extendedPersistenceWeight : 0;

    // 趋势偏离加分
    const trendDeviationBonus = TD ? (trendAnalysis.bias >= 0.9 ? 2 : 1) : 0;

    // 复合异常加分（持续异常 + 趋势偏离）
    const compoundBonus = EP >= 4 && TD && S >= 2 ? 1 : 0;

    // 请求量异常风险加分
    const volumeRiskBonus = volumeRisk.bonus - volumeRisk.penalty;

    raw += extendedPersistenceBonus + trendDeviationBonus + compoundBonus + volumeRiskBonus;

    // ---------- 基于流量规模的风险调节 ----------
    const continuousRiskAdjustment = (rawRisk, yoyStats, dataRelation) => {
        if (!yoyStats || yoyStats.isEmpty) {
            return {risk: rawRisk, confidence: 0.5};
        }

        const {median, cv, logScale} = yoyStats;

        // 对于极端情况，跳过常规调节逻辑
        if (dataRelation.riskLevel === 'CRITICAL') {
            return {
                risk: Math.min(0.98, rawRisk), // 极端情况保持高风险
                confidence: 0.95,
                riskBand: {
                    lower: Math.min(0.95, rawRisk * 0.95),
                    upper: 0.99,
                },
            };
        }

        // 对于高风险情况，减少调节幅度
        if (dataRelation.riskLevel === 'HIGH') {
            return {
                risk: Math.min(0.95, rawRisk * 1.1), // 轻微提升风险
                confidence: 0.85,
                riskBand: {
                    lower: rawRisk * 0.9,
                    upper: Math.min(0.98, rawRisk * 1.2),
                },
            };
        }

        // 规模惩罚函数：小流量降低风险分，大流量略微提升
        const scalePenalty = 1 / (1 + Math.exp(-0.5 * (logScale - 3))); // sigmoid中心在1000

        // 变异性调节：高变异性的服务降低风险敏感度
        const variabilityDiscount = 1 / (1 + cv * 0.3);

        // 最终风险调节
        let adjustedRisk = rawRisk * scalePenalty * variabilityDiscount;

        // 置信度概念
        const confidence = Math.min(1, logScale / 5); // 流量越大置信度越高

        return {
            risk: adjustedRisk,
            confidence: confidence,
            riskBand: {
                lower: adjustedRisk * (1 - (1 - confidence) * 0.3),
                upper: adjustedRisk * (1 + (1 - confidence) * 0.3),
            },
        };
    };

    // ---------- 映射到0-1范围的风险概率 ----------
    // 根据数据关系调整映射参数
    let maxExpectedRaw = 20; // 增加最大期望值
    let k = 0.8; // 控制曲线陡峭度
    let threshold = 6; // 降低50%概率对应的raw值

    // 对于高风险场景，使用更敏感的映射
    if (dataRelation.riskLevel === 'HIGH' || dataRelation.riskLevel === 'CRITICAL') {
        k = 1.2; // 更陡峭的曲线
        threshold = 4; // 更低的阈值
    } else if (dataRelation.riskLevel === 'MEDIUM') {
        k = 1.0;
        threshold = 5;
    }

    let baseRisk = 1 / (1 + Math.exp(-k * (raw - threshold)));

    // 应用基于流量规模的调节
    let riskResult = {risk: baseRisk, confidence: 0.5};
    if (cfg.enableAdaptiveTuning && yoyStats) {
        riskResult = continuousRiskAdjustment(baseRisk, yoyStats, dataRelation);
    }

    // 数据关系置信度调节
    const finalConfidence = Math.min(riskResult.confidence, dataRelation.confidence);
    riskResult.confidence = finalConfidence;

    // 确保风险值在合理范围内
    let risk = riskResult.risk;
    if (risk < 0.01) risk = 0.01;
    if (risk > 0.99) risk = 0.99;
    risk = Math.round(risk * 1000) / 1000;

    return {
        risk,
        confidence: riskResult.confidence || 0.5,
        riskBand: riskResult.riskBand || {lower: risk * 0.7, upper: risk * 1.3},
        raw,
        S,
        M,
        P,
        EP, // 扩展持续性评分
        TD, // 趋势偏离标志
        consCount,
        a,
        b,
        perPoint,
        trendBias: trendAnalysis, // 趋势偏离详细信息
        adaptiveInfo, // 自适应调节信息
        dataRelation, // 数据关系分析信息
        preprocessing: preprocessed, // 预处理信息
        details: {
            medianSigma: median(sigmas),
            cusumTriggered,
            windowN: N,
            config: cfg,
            extendedPersistenceBonus,
            trendDeviationBonus,
            compoundBonus,
            volumeRiskBonus,
            trendAnalysis,
            volumeRisk: volumeRisk,
            riskMappingInfo: {
                rawScore: raw,
                baseRisk: baseRisk,
                finalRiskProbability: risk,
                sigmoidK: k,
                sigmoidThreshold: threshold,
            },
        },
    };
}

// ---------- 智能告警策略函数 ----------
function intelligentAlerting(result) {
    const alerts = [];
    const {risk, confidence, S, EP, TD, trendBias, consCount, adaptiveInfo} = result;

    // 根据置信度调整告警级别
    const adjustLevelByConfidence = (baseLevel) => {
        if (confidence < 0.3) {
            // 低置信度降级
            if (baseLevel === 'CRITICAL') return 'WARNING';
            if (baseLevel === 'WARNING') return 'INFO';
        }
        return baseLevel;
    };

    // 复合异常 - 最高优先级
    if (EP >= 5 && TD && S >= 3) {
        alerts.push({
            level: adjustLevelByConfidence('CRITICAL'),
            type: 'COMPOUND_ANOMALY',
            message: `检测到严重复合异常：连续${consCount}分钟异常，趋势${
                trendBias.direction
            }向偏离${(trendBias.bias * 100).toFixed(1)}%`,
            priority: 1,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`,
            confidence: confidence,
            adaptiveInfo: adaptiveInfo,
        });
    }
    // 严重持续异常
    else if (EP >= 5) {
        alerts.push({
            level: adjustLevelByConfidence('WARNING'),
            type: 'PERSISTENT_ANOMALY',
            message: `连续${consCount}分钟异常，建议立即检查`,
            priority: 2,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`,
            confidence: confidence,
        });
    }
    // 趋势性异常
    else if (TD && trendBias.bias >= 0.9) {
        alerts.push({
            level: adjustLevelByConfidence('WARNING'),
            type: 'STRONG_TREND_DEVIATION',
            message: `检测到强趋势偏离：${(trendBias.bias * 100).toFixed(1)}%的时间点${
                trendBias.direction
            }向异常`,
            priority: 2,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`,
            confidence: confidence,
        });
    }
    // 一般趋势偏离
    else if (TD) {
        alerts.push({
            level: 'INFO',
            type: 'TREND_DEVIATION',
            message: `检测到趋势性偏离：${trendBias.direction}向偏离${(
                trendBias.bias * 100
            ).toFixed(1)}%`,
            priority: 3,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`,
            confidence: confidence,
        });
    }
    // 中等持续异常
    else if (EP >= 3) {
        alerts.push({
            level: 'INFO',
            type: 'MEDIUM_PERSISTENT',
            message: `连续${consCount}分钟异常，请关注`,
            priority: 3,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`,
            confidence: confidence,
        });
    }
    // 高风险单点异常
    else if (risk >= 0.8 && confidence >= 0.5) {
        alerts.push({
            level: adjustLevelByConfidence('WARNING'),
            type: 'HIGH_RISK_SPIKE',
            message: `检测到高风险异常点，风险概率${(risk * 100).toFixed(1)}%，置信度${(
                confidence * 100
            ).toFixed(0)}%`,
            priority: 2,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`,
            confidence: confidence,
        });
    }
    // 通用低风险提醒
    else if (risk >= 0.2) {
        alerts.push({
            level: 'INFO',
            type: 'LOW_RISK',
            message: `检测到轻微异常，风险概率${(risk * 100).toFixed(1)}%`,
            priority: 4,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`,
            confidence: confidence,
        });
    }

    // 如果是小流量服务，添加额外说明
    if (adaptiveInfo && adaptiveInfo.yoyStats && adaptiveInfo.yoyStats.median < 100) {
        alerts.forEach((alert) => {
            alert.note = `小流量服务(中位数:${adaptiveInfo.yoyStats.median.toFixed(
                0
            )})，已自动调整检测灵敏度`;
        });
    }

    return alerts;
}

// ---------- 使用示例 ----------
/*
// 基础用法
const currentWindow = [100, 105, 98, 112, 120, 135, 128, 115, 108, 95]; // 当前30分钟数据
const yoyWindow = [95, 100, 102, 98, 105, 110, 108, 103, 99, 96];     // 上周同时段数据

const result = detectRiskFromWindows(
  currentWindow,
  yoyWindow,
  {
    targetConsecutiveMinutes: 5,        // 关注连续5分钟异常
    trendDeviationWindow: 10,           // 用10分钟窗口检测趋势偏离
    trendBiasThreshold: 0.8,            // 80%同向偏离才认为是趋势异常
    extendedPersistenceWeight: 1.5,     // 扩展持续性权重
    enableAdaptiveTuning: true,         // 启用自适应调节（默认开启）
    adaptiveStrength: 1.0                // 调节强度，0-1之间
  }
);

// 智能告警
const alerts = intelligentAlerting(result);
alerts.forEach(alert => {
  console.log(`[${alert.level}] ${alert.type}: ${alert.message}`);
  console.log(`  风险: ${alert.riskPercentage}, 置信度: ${(alert.confidence * 100).toFixed(0)}%`);
  if (alert.note) {
    console.log(`  注: ${alert.note}`);
  }
});

// 检查关键指标
console.log('=== 风险评估结果 ===');
console.log('风险概率:', (result.risk * 100).toFixed(1) + '%');
console.log('置信度:', (result.confidence * 100).toFixed(0) + '%');
console.log('风险区间:', `[${(result.riskBand.lower * 100).toFixed(1)}%, ${(result.riskBand.upper * 100).toFixed(1)}%]`);
console.log('连续异常分钟数:', result.consCount);
console.log('扩展持续性评分:', result.EP);
console.log('趋势偏离:', result.TD ? '是' : '否');
if (result.TD) {
  console.log('偏离方向:', result.trendBias.direction);
  console.log('偏离程度:', (result.trendBias.bias * 100).toFixed(1) + '%');
}

// 查看自适应调节信息
if (result.adaptiveInfo && result.adaptiveInfo.applied) {
  console.log('=== 自适应调节信息 ===');
  const stats = result.adaptiveInfo.yoyStats;
  const adj = result.adaptiveInfo.adjustments;
  console.log('流量规模中位数:', stats.median.toFixed(0));
  console.log('变异系数(CV):', stats.cv.toFixed(2));
  console.log('相对MAD:', stats.relativeMAD.toFixed(2));
  console.log('规模调节系数:', adj.scale.toFixed(2));
  console.log('变异性调节系数:', adj.variability.toFixed(2));
  console.log('最小有意义变化:', adj.combined.minMeaningfulChange.toFixed(1));
}

// 看板显示示例
console.log('=== 看板显示格式 ===');
const riskLevel = result.risk > 0.8 ? '高风险' :
                  result.risk > 0.5 ? '中风险' :
                  result.risk > 0.2 ? '低风险' : '正常';
console.log(`当前风险: ${(result.risk * 100).toFixed(1)}% [${riskLevel}]`);
console.log(`置信度: ${'█'.repeat(Math.round(result.confidence * 10))}${'░'.repeat(10 - Math.round(result.confidence * 10))} ${(result.confidence * 100).toFixed(0)}%`);

// 处理小流量服务的示例
const smallServiceCurrent = [2, 3, 1, 5, 8, 12, 3, 0, 2, 1];  // 小流量服务当前数据
const smallServiceYoy = [1, 2, 2, 3, 2, 4, 2, 1, 1, 2];       // 小流量服务同比数据

const smallResult = detectRiskFromWindows(
  smallServiceCurrent,
  smallServiceYoy,
  {
    enableAdaptiveTuning: true,  // 自动识别为小流量并调整参数
  }
);

console.log('=== 小流量服务示例 ===');
console.log('风险概率:', (smallResult.risk * 100).toFixed(1) + '%');
console.log('置信度:', (smallResult.confidence * 100).toFixed(0) + '%');
if (smallResult.adaptiveInfo) {
  console.log('检测到小流量服务，已自动调整检测参数');
  console.log('调整后z阈值:', smallResult.details.config.z_th.toFixed(2));
  console.log('调整后ratio阈值:', smallResult.details.config.ratio_th.toFixed(2));
}

// 批量服务监控示例
const services = [
  { name: '核心API', current: [10000, 10500, 9800, ...], yoy: [9500, 10000, 10200, ...] },
  { name: '后台任务', current: [50, 45, 52, ...], yoy: [48, 50, 49, ...] },
  { name: '管理后台', current: [5, 3, 8, ...], yoy: [4, 5, 6, ...] }
];

services.forEach(service => {
  const result = detectRiskFromWindows(service.current, service.yoy);
  console.log(`${service.name}: 风险${(result.risk * 100).toFixed(1)}% (置信度:${(result.confidence * 100).toFixed(0)}%)`);
});
*/