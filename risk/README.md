# 流量监控风险检测算法

基于业界最佳实践的流量监控风险检测算法，专门用于请求量异常检测。

## 🎯 核心特性

- ✅ **业界标准阈值**: 基于流量监控最佳实践
- ✅ **智能场景识别**: 自动识别数据格式和异常类型
- ✅ **小流量保护**: 避免小流量服务误报
- ✅ **多维度检测**: 流量变化、持续性、趋势偏离
- ✅ **高精度风险评估**: 提供置信度和风险区间

## 📊 风险评估标准

| 流量变化 | 风险级别 | 风险概率 | 说明 |
|----------|----------|----------|------|
| ≤20% | 🟢 正常 | 1-5% | 正常业务波动 |
| 20-50% | 🟡 关注 | 5-25% | 需要关注的增长 |
| 50-100% | 🟠 中风险 | 25-50% | 中等风险 |
| ≥100% | 🔴 高风险 | ≥80% | 极高风险(翻倍) |

## 🚀 快速开始

### 基本用法

```javascript
const { detectRiskFromWindows } = require('./test-risk-detector.js');

// 当前时间窗口的请求量
const currentWindow = [1200, 1250, 1180, 1300, 1220];
// 同比时间窗口的请求量  
const yoyWindow = [1000, 1000, 1000, 1000, 1000];

const result = detectRiskFromWindows(currentWindow, yoyWindow);

console.log('风险概率:', (result.risk * 100).toFixed(1) + '%');
console.log('置信度:', (result.confidence * 100).toFixed(1) + '%');
console.log('异常类型:', result.dataRelation.relationType);
```

### 高级配置

```javascript
const result = detectRiskFromWindows(currentWindow, yoyWindow, {
    targetConsecutiveMinutes: 5,        // 目标连续异常分钟数
    trendDeviationWindow: 10,           // 趋势偏离检测窗口
    enableAdaptiveTuning: true,         // 启用自适应调节
    adaptiveStrength: 1.0               // 调节强度
});
```

## 🧪 运行测试

### 基本测试命令
```bash
# 运行所有单元测试
npm test

# 或者直接使用 node
node test-risk-detector.js
```

### 其他测试命令
```bash
# 详细模式
npm run test:verbose

# 监听模式 (需要安装 nodemon)
npm install nodemon
npm run test:watch

# 快速演示
npm run demo
```

## 📋 测试用例覆盖

单元测试包含32个真实生产环境场景：

### 🟢 基础流量监控场景
1. **正常流量波动** (±5%)
2. **轻微流量增长** (+15%)
3. **需要关注的流量增长** (+25%)
4. **中等风险流量增长** (+50%)
5. **高风险流量增长** (+80%)
6. **极高风险 - 流量翻倍** (+100%)

### 🔴 异常检测场景
7. **服务异常 - 流量大幅下降** (-70%)
8. **极端异常数据 - 万倍暴增**
9. **DDoS攻击模式 - 百倍暴增**
10. **流量波动性检测**
11. **瞬时流量尖峰 - 单点异常**
12. **流量雪崩效应**

### 🏷️ 特殊服务类型
13. **小流量服务正常波动**
14. **小流量服务异常增长**
15. **高流量服务正常波动** (百万级)
16. **高流量服务异常暴增** (百万级)
17. **极低流量服务** (个位数)
18. **新服务 - 无历史数据**

### 📊 数据格式处理
19. **正常倍数格式YoY**
20. **正常百分比格式YoY**
21. **数据不一致** (监控系统故障)

### 🔧 复杂业务场景
22. **服务故障恢复模式**
23. **周期性流量波动** (业务高峰)
24. **缓慢流量泄露** (内存泄露等)
25. **流量断崖式下跌** (服务宕机)
26. **微服务级联故障**
27. **流量突发后稳定** (营销活动)
28. **地域性流量异常** (网络分区)

### ⚙️ 系统鲁棒性
29. **边界条件 - 零流量**
30. **数据质量检测 - 缺失值处理**
31. **持续异常检测**
32. **置信度评估**

## 📈 输出结果说明

```javascript
{
  risk: 0.25,                    // 风险概率 (0-1)
  confidence: 0.8,               // 置信度 (0-1)
  riskBand: {                    // 风险区间
    lower: 0.2,
    upper: 0.3
  },
  dataRelation: {                // 数据关系分析
    relationType: 'TRAFFIC_MONITORING',
    flowChangePercent: 25.0,     // 流量变化百分比
    isLowTrafficService: false,  // 是否小流量服务
    riskLevel: 'MEDIUM'          // 风险级别
  },
  // ... 更多详细信息
}
```

## 🔧 算法配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `ewmaSpan` | 3 | 指数加权移动平均窗口 |
| `z_th` | 2.5 | Z-score阈值 |
| `ratio_th` | 0.12 | 比率阈值 |
| `targetConsecutiveMinutes` | 5 | 目标连续异常分钟数 |
| `enableAdaptiveTuning` | true | 启用自适应调节 |

## 📞 技术支持

如有问题或建议，请查看测试用例或联系开发团队。
